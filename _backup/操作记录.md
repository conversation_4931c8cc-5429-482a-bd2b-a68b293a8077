请重新深度、全面整理整个项目，给出整个项目的优化建议文档，要求如下：
- 将所有非项目文件按原目录结构都移动到 @/_backup 目录下
- 重新规范、标准、清晰的调整每个子包的目录结构和文件命名，使其符合标准化规范、统一化
- 重新梳理所有测试命名，统一在 __tests__ 目录下，命名也要统一、清晰、标准、规范
- 将所有工具类、工具方法、工具函数、工具包、工具模块、类型声明、错误处理、常量声明、日志记录等都移动到 @/packages/shared/ 里面对应的子包里面
- 重新梳理所有子包的文件，确保每个子包和模块的单一原则，保留其核心功能和逻辑
- 重新整理每个子包的代码，使其符合标准化规范
- 将所有子包里面的技术说明都统一移动到 @/docs/ 里面，并且符合标准化规范
- 所有缺少子包，请新增新的子包，重新整理新的功能和逻辑划分，确保每个子包职责分明，不要交叉，不要重叠
- 确保所有功能和逻辑都齐全、完整、清晰、可执行、可落地、可直接达到生产标准
- 所有的梳理的工作都要按功能、逻辑、子包、功能、文件的顺序有条理的梳理清楚，要标注原路径，优化后的路径，优化理由等信息
不要做任何修改和移动操作，只在根目录梳理【项目优化文档.md】文档内容
可以拆分多个子任务完成，严禁幻觉，认真执行
rewrite prompt 用中文




请对当前项目进行全面的结构分析和优化规划，生成一份详细的《项目优化文档.md》，包含以下具体要求：

## 分析范围和目标
1. **项目现状分析**：深入分析当前项目的目录结构、文件组织、代码架构
2. **标准化重构规划**：基于现代软件工程最佳实践，制定标准化的项目结构

## 具体优化要求

### 1. 文件分类和迁移规划
- **非项目文件处理**：识别所有与核心业务无关的文件（如临时文件、备份文件、配置草稿等），规划迁移到 `_backup/` 目录，保持原有目录结构
- **核心业务文件保留**：明确哪些文件属于核心业务逻辑，应保留在主项目结构中

### 2. 包结构标准化
- **packages/core/**：仅保留核心业务逻辑，遵循单一职责原则
- **packages/shared/**：统一管理所有共享资源，包括：
  - 工具函数和工具类 (`utils/`)
  - 类型定义 (`types/`)
  - 常量声明 (`constants/`)
  - 错误处理 (`errors/`)
  - 日志记录 (`logging/`)
- **目录命名规范**：使用kebab-case命名，确保语义清晰

### 3. 测试结构统一化
- **测试目录标准**：所有测试文件统一放置在 `__tests__/` 目录下
- **测试文件命名**：采用 `[模块名].test.[扩展名]` 或 `[功能名].spec.[扩展名]` 格式
- **测试结构对应**：测试目录结构应与源码目录结构保持一致

### 4. 文档标准化
- **技术文档集中**：将所有技术说明、API文档、架构文档迁移到 `docs/` 目录
- **文档分类**：按功能模块、API参考、开发指南等进行分类组织
- **文档格式统一**：采用Markdown格式，遵循统一的文档模板

### 5. 模块职责划分
- **识别缺失模块**：分析当前功能，识别需要新增的子包
- **职责边界清晰**：确保每个子包职责单一，避免功能重叠和交叉依赖
- **依赖关系优化**：梳理模块间依赖关系，减少循环依赖

### 6. 统一技术栈要求
- **构建工具**：Vite 7.0.4
- **文档工具**：VitePress 2.0.0-alpha.8
- **开发语言**：TypeScript 5.3+ 严格模式
- **测试框架**：Vitest 3.2.4 + Playwright
- **包管理器**：pnpm 8.15.0
- **版本信息**：0.1.0 初始版本
- **NPM组织**：@micro-core 统一命名

## 输出格式要求

### 文档结构
```
# 项目优化文档

## 1. 项目现状分析
## 2. 优化方案总览
## 3. 详细迁移计划
## 4. 新增模块规划
## 5. 标准化规范
## 6. 实施建议
```

### 每个优化项目必须包含
- **原始路径**：当前文件/目录的完整路径
- **目标路径**：优化后的目标路径
- **优化理由**：详细说明为什么要进行此项调整
- **影响评估**：分析此项调整对其他模块的影响
- **实施优先级**：标注实施的先后顺序

## 执行约束
- **仅生成文档**：不执行任何实际的文件移动或修改操作
- **基于现实**：所有建议必须基于项目实际情况，禁止虚构不存在的文件或功能
- **可执行性**：所有建议必须具备可操作性，能够直接指导实际重构工作
- **生产就绪**：优化后的结构应符合生产环境标准

## 任务分解
可以将此任务分解为以下子任务：
1. 项目结构现状分析
2. 文件分类和迁移规划
3. 包结构重组方案
4. 测试结构标准化
5. 文档整理规划
6. 新增模块设计
7. 实施路线图制定

请严格按照上述要求执行，确保输出的优化文档具有实际指导价值。






为项目中的所有子包实现完整的单元测试覆盖，要求每个子包的测试覆盖率必须达到100%，所有测试用例必须100%通过。测试代码需要严格验证功能逻辑，杜绝任何虚假测试或测试遗漏。确保测试结果真实可靠，能够全面验证子包功能的正确性和稳定性。测试报告需包含详细的覆盖率分析和测试结果统计。



# Micro-core Monorepo Testing Plan

## Notes
- User requires 100% unit test coverage for all sub-packages.
- All tests must strictly validate functional logic; no superficial or missing tests.
- Test results must be reliable and fully verify correctness and stability.
- Reports must include detailed coverage analysis and result statistics.

## Task List
- [ ] Audit all sub-packages to inventory existing tests and coverage.
- [ ] Identify missing or inadequate test cases for each sub-package.
- [ ] Implement comprehensive unit tests to achieve 100% coverage per sub-package.
- [ ] Strictly validate all functional logic and edge cases in tests.
- [ ] Run all tests and verify 100% pass rate.
- [ ] Generate and review detailed coverage and result reports.
- [ ] Deliver reports and confirm requirements are fully met.

## Current Goal
Audit sub-packages and assess current test coverage.



@/全面梳理清单.md  @/packages目录深度分析与重构清单.md  @/packages重构实施手册.md  @/README-重构指南.md  请根据这四个文档完整内容，全面、深度完成 @/packages  包的完整、清晰的优化工作，严禁幻觉，认真执行 




全面、深度检查 packages 里面所有文件、内容的完整性、合理性、规范性、标准性：
- 请按子包、再按子目录、再按文件的顺序依次进行检查，将结果输出到根目录“全面梳理清单.md”
- 每一个功能和逻辑都要梳理清楚，按照优先级罗列清楚，每个都要给出完整的完善、修复、优化建议
- 如果存在不合理的目录架构，也要给出调整建议，如果存在冗余的文件夹、文件、代码、注释问题，都要清理干净
- 严禁幻觉，严禁主观臆测，严禁大幅度删减核心功能和内容
- 请将工具类的，类型定义的都归置到 @/packages/shared/ 里面对应的子包里面
- 要保证每一个子包的核心功能和逻辑都要梳理清楚，其他的都要移动到对应的子包中，如果不存在，请新增子包
- 每一处改动建议都标注清楚



@/全面梳理清单.md  @/packages目录深度分析与重构清单.md  @/packages重构实施手册.md  @/README-重构指南.md @/.kiro/specs/packages-optimization/requirements.md @/.kiro/specs/packages-optimization/design.md @/.kiro/specs/packages-optimization/tasks.md   请严格遵循这7个文档完整内容，全面、深度完成 @/packages  包的完整、清晰的优化工作，严禁幻觉，认真执行


@/.kiro/specs/packages-optimization/tasks.md 根据 tasks.md 文档 task 列表，依次执行，严禁幻觉，完成之后，请认真更新 task 的状态



根据 tasks.md 文档中的 task 列表，严格按照所列顺序依次执行每个任务。确保每个步骤都准确无误地完成，避免任何猜测或虚构内容。每完成一个 task 后，立即在文档中更新该 task 的状态。整个过程中需保持高度严谨，严禁任何形式的幻觉或主观臆断。



请基于以下7个关键文档的完整内容，对 `/packages` 目录进行全面、系统的优化重构工作：

**参考文档：**
1. `/全面梳理清单.md` - 整体梳理指导
2. `/packages目录深度分析与重构清单.md` - 目录结构分析
3. `/packages重构实施手册.md` - 具体实施步骤
4. `/README-重构指南.md` - 重构总体指南
5. `/.kiro/specs/packages-optimization/requirements.md` - 需求规格
6. `/.kiro/specs/packages-optimization/design.md` - 设计方案
7. `/.kiro/specs/packages-optimization/tasks.md` - 任务清单

**执行要求：**
- 严格按照文档内容执行，不得偏离或添加未明确要求的功能
- 完整阅读并理解所有7个文档的内容后再开始工作
- 确保代码重构的完整性和一致性
- 保持现有功能不受影响的前提下进行优化
- 提供详细的执行计划和进度跟踪
- 如遇到文档间冲突或不明确之处，请先询问澄清

**预期成果：**
- `/packages` 目录结构清晰、模块化程度高
- 代码组织符合最佳实践
- 依赖关系明确且合理
- 文档和代码保持同步更新

start tasks.md task，不要中断，严禁幻觉，一次性完成所有任务



rewrite prompt 用中文


请对 packages 目录下的所有文件和内容进行全面、深度的完整性、合理性、规范性和标准性检查，并生成详细的梳理清单：

**检查范围和顺序：**
- 按照子包 → 子目录 → 文件的层级顺序依次进行检查
- 将检查结果输出到项目根目录的"全面梳理清单.md"文件中

**检查内容要求：**
1. **功能和逻辑梳理**：
   - 详细分析每个功能模块和业务逻辑
   - 按优先级（高/中/低）对问题进行分类
   - 为每个问题提供具体的完善、修复、优化建议
   - 包含实施步骤和预期效果

2. **架构和组织优化**：
   - 识别不合理的目录结构并提供重构建议
   - 清理冗余的文件夹、文件、代码和注释
   - 确保代码组织符合项目规范

3. **代码重组要求**：
   - 将工具类函数迁移到 `packages/shared/utils/` 对应子包
   - 将类型定义迁移到 `packages/shared/types/` 对应子包
   - 确保 `packages/core/` 仅保留核心业务逻辑
   - 如需新增子包，请明确说明创建理由和结构

**输出格式要求**：
- 每个改动建议必须标注清楚位置、原因和具体操作步骤
- 使用 Markdown 格式，包含目录结构、优先级标识和进度跟踪
- 提供改动前后的对比说明

**质量保证原则**：
- 基于实际代码内容进行分析，严禁主观臆测
- 保留所有核心功能，不得删减重要业务逻辑
- 所有建议必须有明确的技术依据和改进价值





@/Users/<USER>/Desktop/micro-core/packages/core/ 全面、深度、认真的优化 core 里面每个文件和每一行代码：
- 将有一些公共的文件、函数、代码抽离到 @/packages/ 里面对应的子包里面
- 将所有无效文件夹、文件、代码都清理干净
- 重新整理整个目录结构，保持整洁、清晰，使其更加标准、规范
- 完善所有的测试文件，确保覆盖率和通过率都是 100%
不要出现幻觉，认真执行 



@/docs  全面、深度检查 docs 文档目录，保证现在的风格、排版、导航，完善以下功能：
- 搜索功能
- 对照中文文档，全面补全英文文档
- 确保中文、英文所有链接都正确
- 将冗余、多余、无效的文件按原目录路径移动到 @/_backup 目录下
- 清除所有文档中无效内容和自由发挥、幻觉的内容，确保清晰、整洁、全面
- 确保文档系统达到生产的标准
不要出现幻觉，认真按要求执行




请再次认真检查 @/Users/<USER>/Desktop/micro-core/packages/core/ 包的所有文件
- 确保功能和逻辑齐全
- 将所有工具函数或者工具方法都移动到 @/Users/<USER>/Desktop/micro-core/packages/shared/ 下面对应的子包里面
- 将所有 @/Users/<USER>/Desktop/micro-core/packages/core/src/types/ 里面的类型和枚举定义都移动到 @/Users/<USER>/Desktop/micro-core/packages/shared/ 下面对应的子包里面
- 认真、全面的清理 @/Users/<USER>/Desktop/micro-core/packages/core/  里面冗余或者无效的文件夹、文件、以及无效的代码和逻辑
- 最后再次全面、深度、认真的检查 @/Users/<USER>/Desktop/micro-core/packages/core/ 目录下所有文件内容，确保功能齐全、逻辑完整，测试完全通过，达到直接可以生产的标准
不要出现幻觉，认真优化




请再次认真检查 @/Users/<USER>/Desktop/micro-core/packages/sidecar/ 包的所有文件
- 确保功能和逻辑齐全
- 将所有工具函数或者工具方法都移动到 @/Users/<USER>/Desktop/micro-core/packages/shared/ 下面对应的子包里面
- 将所有 @/Users/<USER>/Desktop/micro-core/packages/core/src/types/ 里面的类型和枚举定义都移动到 @/Users/<USER>/Desktop/micro-core/packages/shared/ 下面对应的子包里面
- 认真、全面的清理 @/Users/<USER>/Desktop/micro-core/packages/sidecar/  里面冗余或者无效的文件夹、文件、以及无效的代码和逻辑
- 最后再次全面、深度、认真的检查 @/Users/<USER>/Desktop/micro-core/packages/sidecar/ 目录下所有文件内容，确保功能齐全、逻辑完整，测试完全通过，达到直接可以生产的标准
- 保留边车模式的核心功能，其他的可以以插件或者适配器增强的，请移到到 @/Users/<USER>/Desktop/micro-core/packages/ 对应的里面的子包里面，或者没有合适的子包，请新建新的子包
不要出现幻觉，认真优化




请对 `/Users/<USER>/Desktop/micro-core/packages/core/` 核心包进行最终的代码重构和优化，确保其达到生产就绪标准。基于我们之前完成的重构工作（将非核心功能迁移到插件包，保留核心必需功能）和测试验证（19个测试全部通过），现在需要进行最后的代码整理和优化：

**具体任务要求：**

1. **功能完整性检查**：
   - 逐个检查 `packages/core/src/` 下的每个文件和目录
   - 验证所有核心功能（EventBus、AppRegistry、LifecycleManager、PluginSystem、MicroCoreKernel）的逻辑完整性
   - 确保所有必需的方法、属性和接口都已正确实现
   - 检查文件间的依赖关系和导入导出是否正确

2. **工具函数迁移**：
   - 识别 `packages/core/src/` 中所有通用的工具函数和辅助方法
   - 将这些工具函数迁移到 `packages/shared/` 下的相应子包中：
     - 通用工具函数 → `packages/shared/utils/`
     - 错误处理工具 → `packages/shared/errors/`
     - 性能监控工具 → `packages/shared/performance/`
     - 日志工具 → `packages/shared/logger/`
   - 更新所有相关的导入路径
   - 确保迁移后的功能正常工作

3. **类型定义迁移**：
   - 将 `packages/core/src/types/` 目录下的所有类型定义和枚举迁移到 `packages/shared/types/` 下：
     - 应用相关类型 → `packages/shared/types/app.ts`
     - 通信相关类型 → `packages/shared/types/communication.ts`
     - 生命周期类型 → `packages/shared/types/lifecycle.ts`
     - 插件系统类型 → `packages/shared/types/plugin.ts`
     - 沙箱相关类型 → `packages/shared/types/sandbox.ts`
     - 路由相关类型 → `packages/shared/types/router.ts`
     - 通用类型和枚举 → `packages/shared/types/common.ts`
   - 更新所有文件中的类型导入路径
   - 确保类型定义的一致性和完整性

4. **代码清理和优化**：
   - 删除所有冗余的文件和目录
   - 移除未使用的导入语句和变量
   - 清理注释掉的代码和调试代码
   - 统一代码风格和格式
   - 优化文件结构，确保目录层次清晰
   - 移除重复的逻辑和代码

5. **最终质量验证**：
   - 运行完整的测试套件，确保所有测试通过（目标：19/19 测试通过）
   - 检查 TypeScript 编译是否无错误和警告
   - 验证所有导入导出路径的正确性
   - 确保核心包的 API 接口稳定且完整
   - 验证与其他包（shared、plugins）的依赖关系正确
   - 检查包的构建和打包是否正常

**预期结果：**
- `packages/core/` 只包含微前端的核心运行时逻辑
- 所有通用工具和类型定义已迁移到 `packages/shared/`
- 代码结构清晰，无冗余文件和逻辑
- 所有测试通过，功能完整可用
- 达到生产环境部署标准

**注意事项：**
- 基于实际的文件内容进行操作，不要假设或创造不存在的代码
- 保持向后兼容性，确保 API 接口不被破坏
- 迁移过程中要保持功能的完整性
- 每次重大修改后都要运行测试验证
- 记录所有重要的修改和迁移操作







基于@/docs/文档系统检查总结报告.md、@/docs/文档系统完整性检查报告.md和@/docs/文档修复执行计划.md三份文档的分析结果，对@/docs目录下的所有文档进行全面修复。要求：1) 严格依据三份基准文档指出的问题点进行修正；2) 确保修复后的文档内容完整、逻辑清晰、格式规范；3) 修复范围需覆盖所有文档的文本内容、数据结构、引用关系和版本信息；4) 保持原始文档的核心信息不变，仅修正错误和不一致之处；5) 生成详细的修复日志记录所有变更内容。注意：所有修改必须基于现有文档证据，不得添加未经证实的内容。



请对 `/Users/<USER>/Desktop/micro-core/packages/core/` 目录下的精简后的微前端核心包进行全面的测试工作。基于我们刚完成的重构（将非核心功能迁移到插件包，保留核心必需功能），请执行以下测试任务：

**测试范围：**
- 核心运行时组件（kernel, app-registry, lifecycle-manager, plugin-system, app-loader, resource-manager, error-handler）
- 基础事件总线（communication/event-bus.ts）
- 基础沙箱接口（sandbox/base-sandbox.ts）
- 核心类型定义和工具函数
- 主导出接口（index.ts）

**测试类型要求：**
1. **单元测试**：测试每个类和方法的独立功能
2. **集成测试**：测试组件间的协作和数据流
3. **功能测试**：验证核心业务逻辑（应用注册、生命周期管理、插件系统等）
4. **边界测试**：测试极端输入值、空值、错误参数等边界情况
5. **错误处理测试**：验证异常情况下的错误捕获和恢复机制
6. **性能测试**：测试关键操作的执行时间和内存使用

**具体要求：**
- 使用现有的测试框架（Vitest）和配置
- 确保所有测试用例都能通过（100% 通过率）
- 实现 100% 的代码覆盖率
- 重点测试重构后可能受影响的功能
- 验证精简后的 API 接口完整性
- 测试插件系统的基础功能（为后续插件开发提供保障）

**注意事项：**
- 基于当前精简后的代码结构编写测试
- 不要测试已迁移到插件包的功能
- 确保测试代码质量，避免无效或重复的测试
- 如发现代码问题，请修复后再进行测试
- 提供测试报告，包括覆盖率统计和性能指标




@/docs/zh/
@/_backup
"请对文档系统进行以下优化和重构：
1. 文件整理：
- 将所有中文文档文件按照规则移动到 @/docs/zh/ 目录下合适位置
- 将冗余、重复或废弃文件按原目录结构移动到 @/_backup 目录中

2. 配置同步：
- 确保中文导航配置与英文配置完全同步
- 保持中英文文档结构的一致性

3. 目录规范：
- 全面检查并重构整个docs目录结构
- 确保目录组织符合标准化规范
- 保证新结构清晰、可执行且易于维护

4. 执行要求：
- 保留所有有效文档内容
- 确保迁移后所有链接和引用保持有效
- 建立清晰的文件命名和分类标准
- 不要出现幻觉，严禁误操作"


请认真深度、全面检查 @/docs 目录下所有文档文件，确保所有中文、英文文档文件齐全，两边内容保持一致。所有文档内容完整、清晰、详细。在合适的位置要保证架构图、流程图、时序图等 ASCII art设计图完整、齐全，不要出现需要第三方插件的设计图。不要大幅度改动现有结构和目录，严禁出现幻觉，认真执行上述要求任务



全面检查 @/docs 目录下的所有中文和英文文档文件，确保以下要求严格满足：  
1. 完整性验证：核对中英文版本内容完全对应，无缺失文档或章节  
2. 内容质量：所有技术描述需清晰准确，关键流程需配备ASCII art架构图/流程图/时序图（禁用需第三方插件的图表）  
3. 格式规范：保持现有目录结构不变，仅允许必要的文字优化和图表补充  
4. 一致性检查：术语翻译、代码示例、版本号等中英文内容必须同步更新  
5. 深度审查：重点验证复杂技术场景的文档是否具备：  
   - 前置条件说明  
   - 分步骤操作指南  
   - 异常处理方案  
   - 配套示意图表  
发现任何不一致/缺失/错误需生成详细差异报告，标注具体文件路径和问题位置。


请对文档系统进行以下优化和重构：
- 文件整理：
  * 将所有中文文档文件按照规则移动到 @/docs/zh/ 目录下合适位置
  * 将所有英文文档文件按照规则移动到 @/docs/en/ 目录下合适位置
  * 将冗余、重复或废弃文件按原目录结构移动到 @/_backup 目录中
- 确保中文导航配置与英文配置完全同步
- 确保目录组织符合标准化规范
- 保证新结构清晰、可执行且易于维护
- 确保迁移后所有链接和引用保持有效
- 建立清晰的文件命名和分类标准
- 不要出现幻觉，严禁误操作


1. 配置同步：
- 确保中文导航配置与英文配置完全同步
- 保持中英文文档结构的一致性

2. 目录规范：
- 全面检查并重构整个docs目录结构
- 确保目录组织符合标准化规范
- 保证新结构清晰、可执行且易于维护
3. 执行要求：
- 建立清晰的文件命名和分类标准
- 不要出现幻觉，严禁误操作








@/Users/<USER>/Desktop/micro-core/packages/core/完整优化清单.md 根据当前优化清单，完整、全面、深入优化 @/Users/<USER>/Desktop/micro-core/packages/core/项目所有文件，不要出现幻觉，确保可执行、可测试、可落地







基于 `/Users/<USER>/Desktop/micro-core/packages/core/完整优化清单.md` 中的详细分析和优化建议，对 `/Users/<USER>/Desktop/micro-core/packages/core/` 目录下的所有源代码文件进行系统性优化实施。

**执行要求**：

1. **严格按照优先级执行**：
   - 首先修复所有 P0 级紧急问题（15项）
   - 然后处理 P1 级重要问题（23项）
   - 最后优化 P2 级改进问题（31项）

2. **具体实施标准**：
   - 每个修改都必须基于优化清单中的具体问题描述和解决方案
   - 使用清单中提供的代码示例作为实施参考
   - 确保所有修改都有对应的验证方式（如测试命令、编译检查等）

3. **代码质量保证**：
   - 所有修改必须保持与现有代码风格一致
   - 遵循 TypeScript 严格模式要求
   - 保持向后兼容性，不破坏现有 API
   - 每个文件修改后都要确保能通过 TypeScript 编译

4. **验证和测试**：
   - 修复测试系统后，确保 `pnpm test` 能正常运行
   - 每个阶段完成后运行相应的验证命令
   - 优先修复阻塞测试执行的问题

5. **实施顺序**：
   - 第一步：修复常量命名不一致问题（SANDBOX_TYPE → SANDBOX_TYPES）
   - 第二步：解决类型循环依赖和导入路径错误
   - 第三步：清理重复代码和错误处理模块
   - 后续按照清单中的详细修复计划逐步实施

**禁止事项**：
- 不要添加清单中未提及的功能
- 不要修改清单中未标识为问题的代码
- 不要进行超出清单范围的重构
- 确保每个修改都有明确的问题对应和解决依据

请逐步实施，每完成一个阶段的修复后报告进度和验证结果。










@/Users/<USER>/Desktop/micro-core/开发设计指导方案.md @/Users/<USER>/Desktop/micro-core/完整目录结构设计.md 根据当前两个文档内容，深度、全面检查@/Users/<USER>/Desktop/micro-core/packages/core/ 目录下所有文件（排除node_modules和dist目录），深度阅读、分析每一行代码，在当前core目录下生成一份“完整优化清单.md”文档，要求从文件粒度，分步骤、分层次、分逻辑、分优先级多个维度给出完整、全面的优化建议，目录结构、功能完整性、逻辑清晰性、代码复用性、功能重复性、整体规范性、项目标准性多个角度完善，保证最后可落地、可执行，达到完全的生产标准。不要出现幻觉，认真执行上述要求工作





请基于 `/Users/<USER>/Desktop/micro-core/开发设计指导方案.md` 和 `/Users/<USER>/Desktop/micro-core/完整目录结构设计.md` 两个设计文档的内容，对 `/Users/<USER>/Desktop/micro-core/packages/core/` 目录进行全面的代码审查和优化分析。

**具体要求：**

1. **代码审查范围**：
   - 深度分析 `packages/core/` 目录下的所有源代码文件
   - 排除 `node_modules`、`dist`、`.git` 等构建和依赖目录
   - 逐文件、逐函数、逐行进行代码质量评估

2. **分析维度**：
   - **目录结构合规性**：对照设计文档检查目录组织是否符合规范
   - **功能完整性**：识别缺失的核心功能模块和API
   - **代码质量**：评估代码可读性、可维护性、性能优化空间
   - **架构一致性**：检查是否遵循设计文档中的架构原则
   - **重复代码识别**：找出可复用和需要重构的代码片段
   - **类型安全性**：TypeScript 类型定义的完整性和准确性
   - **测试覆盖度**：单元测试和集成测试的完备性

3. **输出要求**：
   - 在 `packages/core/` 目录下生成 `完整优化清单.md` 文件
   - 按照**优先级**（P0-紧急、P1-重要、P2-优化）分类问题
   - 按照**文件路径**组织优化建议，便于定位和修改
   - 每个优化项目包含：问题描述、影响分析、具体解决方案、预估工作量
   - 提供可执行的代码示例和重构步骤
   - 确保所有建议都有明确的实施路径，达到生产环境标准

4. **执行标准**：
   - 基于实际代码内容进行分析，避免假设性建议
   - 所有优化建议必须可验证、可测试、可落地
   - 遵循项目现有的技术栈和编码规范
   - 考虑向后兼容性和渐进式改进策略






深度、全面梳理 docs 目录下所有文件（排除node_modules），如何查漏补缺？如何要符合规范？如何确保目录结构和文档信息完整？如何确保内容没有缺失？如何确保逻辑和功能完整？给出一份完整的 AI 开发提示词，方便全面梳理 vitepress 开发的技术文档，保证可落地、可执行、可生产的标准





@/docs 深度、全面的遍历 docs 目录下每一个文件内容（node_modules排除），结合@/开发设计指导方案.md 和@/完整目录结构设计.md 两个文档完整内容，深度、全面、清晰的梳理出待优化点：
1.每个模块、每个文档是否完整、清晰、标准？
2.每个模块、每个文档是否存在冗余和错误信息？
3.每个模块、每个文件的格式、样式是否标准？是否统一？是否符合规范？
4.整个文档系统深浅主题切换是否正常？默认是否跟随系统？切换完目标是否可以缓存？
5.整个文档系统中英文切换功能是否正常？切换的内容是否有异常？切换完目标是否可以缓存？
6.首页的页面内容是否被导航部分遮挡？
7.网站的icon浏览器顶部导航图标是否正常？
8.整个docs的目录结构是否合理？规范？
9.整个docs的目录是否存在冗余、重复、错误的文件夹或者文件？
将上面要求的信息按照每个文件颗粒度进行深度、全面梳理，给出完整、全面、清晰的优化方案，按照不同维度分类，标注详细的优化方案建议，要分步骤、分层级、分优先级的梳理到根目录“文档系统优化清单.md”文档中，不要出现幻觉





@/docs @/文档系统优化清单.md 严格遵循当前文件优化建议方案，深度优化 @/docs 目录下文档系统：
1. VitePress 配置层面 (32个高优先级问题)
深浅主题切换配置不完整，缺少系统跟随和缓存机制
中英文切换功能完全缺失
浏览器图标配置不规范
导航结构过深，用户体验不佳
2. 文档内容完整性 (61个缺失文档)
指南文档缺失: 15个文件 (插件系统、构建集成、最佳实践等)
API文档缺失: 12个文件 (状态管理、插件API、适配器API等)
示例文档缺失: 10个文件 (框架示例、高级示例等)
迁移文档缺失: 12个文件 (配置迁移、生命周期迁移等)
生态系统文档缺失: 12个文件 (插件详解、构建工具集成等)
3. 用户体验问题 (28个中优先级问题)
首页内容可能被导航遮挡
搜索功能不够精准，缺少中文分词支持
缺少响应式设计优化
静态资源未优化
4. 技术架构问题 (13个低优先级问题)
构建性能需要优化
缺少PWA支持
目录结构与设计文档不完全符合
要求不要出现幻觉，依次执行优化步骤，一次性完成所有优化工作





对 @/packages/adapters 目录执行全面深度检查与优化，严格依据 @/开发设计指导方案.md 和 @/完整目录结构设计.md 的规范要求。
存在以下问题：
1) 目录结构太过于混乱；
2) 很多重复和冗余的目录和文件、功能、逻辑、代码；
3) 测试文件、内容过于简洁，不够全面、完整；
4) 全面检查代码质量，涵盖错误处理、性能、安全及可读性；
5) 技术栈严格采用Vite 7.0.4, VitePress 2.0.0-alpha.8, TypeScript, vitest 3.2.4, pnpm 8.15.0。
优化要求具体包括：
1) 逐文件核验功能实现与设计文档的一致性，确保模块行为符合预期；
2) 验证目录结构、文件命名及位置是否符合规范；
3) 深度优化核心逻辑，提升执行效率与可维护性；
4) 全面检查代码质量，涵盖错误处理、性能、安全及可读性；
5) 按文档要求进行结构调整与功能补充；
6) 编写完整测试套件，覆盖功能逻辑、边界条件、错误场景及性能指标，确保100%测试覆盖率；
7) 清理冗余内容，优化目录结构，严禁误删，确保功能完整性；
8) 消除所有结构混乱问题。
检查需基于代码和文档客观分析，不得主观假设，一次性完成所有检查优化工作，确保交付物达到生产环境质量标准。




对 `/packages/adapters` 目录执行全面的代码审查、重构和优化工作，严格遵循 `/开发设计指导方案.md` 和 `/完整目录结构设计.md` 中定义的架构规范和编码标准。

**当前识别的问题：**
1. 目录结构组织混乱，不符合设计文档规范
2. 存在重复和冗余的目录、文件、功能模块和代码逻辑
3. 测试文件内容过于简单，缺乏全面的测试覆盖
4. 代码质量需要全面提升，包括错误处理、性能优化、安全性和可读性

**技术栈要求：**
- Vite 7.0.4
- VitePress 2.0.0-alpha.8  
- TypeScript
- Vitest 3.2.4
- pnpm 8.15.0

**具体优化任务：**

1. **架构一致性验证**
   - 逐个文件检查功能实现是否与设计文档保持一致
   - 验证每个模块的行为是否符合预期的接口规范
   - 确保适配器模式的正确实现

2. **目录结构规范化**
   - 验证目录结构是否符合 `/完整目录结构设计.md` 的规范
   - 检查文件命名约定和文件位置的合理性
   - 重新组织不符合规范的目录和文件

3. **代码质量全面提升**
   - 优化核心业务逻辑，提升执行效率和可维护性
   - 完善错误处理机制，增加适当的异常捕获和处理
   - 进行性能优化，识别和解决潜在的性能瓶颈
   - 提升代码安全性，防范常见的安全漏洞
   - 改善代码可读性，添加必要的注释和文档

4. **测试套件完善**
   - 编写全面的单元测试，覆盖所有功能模块
   - 添加集成测试，验证模块间的协作
   - 包含边界条件测试和异常场景测试
   - 添加性能基准测试
   - 确保测试覆盖率达到100%

5. **重构和清理**
   - 识别并合并重复的功能模块
   - 删除冗余的代码和未使用的文件
   - 重构复杂的函数和类，提升代码质量
   - 确保在清理过程中不误删重要功能

6. **文档和规范对齐**
   - 根据设计文档要求调整代码结构
   - 补充缺失的功能实现
   - 更新相关的类型定义和接口

**执行要求：**
- 基于现有代码和设计文档进行客观分析，避免主观假设
- 在一个完整的工作流程中完成所有检查和优化任务
- 确保最终交付的代码达到生产环境的质量标准
- 在进行任何删除操作前，必须确认不会影响现有功能
- 所有修改都应该有相应的测试验证

**预期成果：**
- 清晰、规范的目录结构
- 高质量、可维护的代码库
- 完整的测试覆盖
- 符合设计文档的功能实现
- 消除所有冗余和重复内容





@/packages/core 全面、深度检查 core 包所有文件内容，要求符合@/开发设计指导方案.md @/完整目录结构设计.md 两个文档要求，完整、全面、深层次优化、完善 core 整个包的逻辑和功能，确保所有的内容都完全，清晰，可达到生产标准，不要出现幻觉，严格按照文档说明，综合分析，开展全面的结构调整和完善工作




对 @/packages/core （目标目录）目录下的所有文件进行全面深度检查，严格遵循 @/开发设计指导方案.md 和 @/完整目录结构设计.md 两份文档的标准要求。具体包括：
1) 逐文件比对功能实现与设计文档的一致性，确保每个模块都符合预期行为；
2) 验证目录层级、文件命名和位置是否符合规范要求；
3) 深度优化核心逻辑，提升代码执行效率和可维护性；
4) 检查代码质量，包括但不限于错误处理、性能优化、安全性和可读性；
5) 根据文档要求进行必要的结构调整和功能补充；
6) 编写完整的测试套件，覆盖所有功能逻辑、边界条件、错误场景和性能指标，确保测试覆盖率达到100%
7) 要对目标目录下所有的目录结构、文件夹、文件进行深度优化和清理，确保整个目录包的干净整洁，严禁一切误删内容的操作，一定要确保内容、功能、逻辑的完整性、清晰性，严禁一切的幻觉
8) 确保整个目标目录严禁存在任何结构混乱问题，查漏补缺，删除冗余、重复、多余的文件、文件夹、内容和功能，严禁一切的误删除动作。
检查过程需基于实际代码和文档内容进行客观分析，不得有任何主观假设。要求一次性完成所有检查、优化和测试工作，确保最终交付物完全符合生产环境质量标准。





对 @/packages/core 目录执行全面深度检查与优化，严格依据 @/开发设计指导方案.md 和 @/完整目录结构设计.md 的规范要求。
存在以下问题：
1) 重构目录结构，确保符合模块化设计原则，消除混乱层级
2) 识别并删除重复/冗余的目录、文件、功能和逻辑代码
3) 完善测试体系，补充单元测试和集成测试，确保覆盖率达标
4) 执行代码质量审查，重点检查：
   - 错误处理机制
   - 性能优化点
   - 安全漏洞
   - 代码可读性与维护性
5) 技术栈限定为：
   - Vite 7.0.4
   - VitePress 2.0.0-alpha.8
   - TypeScript
   - vitest 3.2.4
   - pnpm 8.15.0。
优化要求具体包括：
1) 逐文件核验功能实现与设计文档的一致性，确保模块行为符合预期；
2) 验证目录结构、文件命名及位置是否符合规范；
3) 深度优化核心逻辑，提升执行效率与可维护性；
4) 全面检查代码质量，涵盖错误处理、性能、安全及可读性；
5) 按文档要求进行结构调整与功能补充；
6) 编写完整测试套件，覆盖功能逻辑、边界条件、错误场景及性能指标，确保100%测试覆盖率；
7) 清理冗余内容，优化目录结构，严禁误删，确保功能完整性；
8) 消除所有结构混乱问题。
检查需基于代码和文档客观分析，不得主观假设，一次性完成所有检查优化工作，确保交付物达到生产环境质量标准。










对 @/packages/core 目录执行全面深度检查与优化，严格依据 @/开发设计指导方案.md 和 @/完整目录结构设计.md 的规范要求。
存在以下问题：
1) 目录结构太过于混乱；
2) 很多重复和冗余的目录和文件、功能、逻辑、代码；
3) 测试文件、内容过于简洁，不够全面、完整；
4) 全面检查代码质量，涵盖错误处理、性能、安全及可读性；
5) 技术栈严格采用Vite 7.0.4, VitePress 2.0.0-alpha.8, TypeScript, vitest 3.2.4, pnpm 8.15.0。
优化要求具体包括：
1) 逐文件核验功能实现与设计文档的一致性，确保模块行为符合预期；
2) 验证目录结构、文件命名及位置是否符合规范；
3) 深度优化核心逻辑，提升执行效率与可维护性；
4) 全面检查代码质量，涵盖错误处理、性能、安全及可读性；
5) 按文档要求进行结构调整与功能补充；
6) 编写完整测试套件，覆盖功能逻辑、边界条件、错误场景及性能指标，确保100%测试覆盖率；
7) 清理冗余内容，优化目录结构，严禁误删，确保功能完整性；
8) 消除所有结构混乱问题。
检查需基于代码和文档客观分析，不得主观假设，一次性完成所有检查优化工作，确保交付物达到生产环境质量标准。




对 @/packages/core 目录下的所有文件进行全面深度检查，确保完全符合 @/开发设计指导方案.md 和 @/完整目录结构设计.md 两份文档的要求标准。重点包括：
1) 逐文件核对功能实现与设计文档的一致性；
2) 验证目录结构是否符合规范；
3) 深度优化核心逻辑和功能实现；
4) 确保所有代码达到生产环境质量标准；
5) 进行必要的结构调整和功能完善。
检查过程需严格遵循文档说明，采用综合分析的方法，杜绝任何不符合实际情况的假设或推测。





@/packages/ @/apps @/docs 全面检查当前项目中`@/packages`、`@/apps`、`@/docs`三个目录下的所有子应用和文件夹，严格依据以下四个规范文档进行验证：`开发设计指导方案.md`（功能逻辑完整性）、`完整目录结构设计.md`（目录层级规范）、`优化建议清单.md`（性能优化点）和`结构优化清单.md`（架构合理性）。要求：
1) 逐项核对三个目录下每个子包/子应用的功能模块是否完整实现；
2) 检查目录结构是否与设计文档完全一致；
3) 识别并处理所有空文件夹；
4) 补充缺失的功能模块；
5) 确保代码实现与文档要求100%匹配；
6) 要求所有功能和逻辑都符合微前端架构设计的要求；
7) 确保所有的功能都符合现代开发规范和要求；
8) 确保整个项目严禁存在任何结构混乱问题；
9) 禁止创建微前端项目合规性检查系统；
10) 确保整个项目完全、齐全、清晰、完整、可执行、可落地、可直接达到生产标准。
重点排查文档中明确要求但未实现的条目，禁止任何未经文档授权的虚构实现。请直接执行完善工作




项目当前存在严重结构混乱问题，请结合`开发设计指导方案.md`、`完整目录结构设计.md`、`优化建议清单.md`、`结构优化清单.md`四个文档完整内容，特别关注`优化建议清单.md`和`结构优化清单.md`，按照文档要求请尽快重构！！！
不要出现幻觉，依照文档说明，综合分析，开展全面的优化工作
所有信息都不要出现幻觉，重点要保证整个项目的规范性、完整性、一致性、全面性、易用性、可扩展性





当前项目存在严重结构混乱问题，请严格结合`开发设计指导方案.md`、`完整目录结构设计.md`、`优化建议清单.md`和`结构优化清单.md`四个文档的完整内容，执行以下系统性重构任务：
1. 严格按文档规范重构项目结构，重点解决`优化建议清单.md`指出的目录混乱问题
2. 对照`结构优化清单.md`逐项调整模块组织方式，确保符合标准化要求
3. 保持核心功能完整性，按`完整目录结构设计.md`规范重新组织各层级目录
4. 优化架构时需同时满足`开发设计指导方案.md`的可扩展性要求和`优化建议清单.md`的易用性改进建议
5. 所有修改必须严格基于四个文档的实际内容，不得添加文档未提及的改动
6. 最终交付结构应完全符合：规范目录层级、标准化模块划分、优化后的依赖关系、清晰的接口定义这四项核心标准
7. 重点要确保整个项目的规范性、完整性、一致性、全面性、易用性、可扩展性






当前项目结构、packages子包和各个子包整体结构、apps整体结构、docs整体结构、audit-system整体结构都特别混乱，
请深度严格结合 @/开发设计指导方案.md 和 @/完整目录结构设计.md 两个文档完整的内容，
重新深度和广度两个维度，从文件粒度层面整个完整的分析、整理整个项目，给出一份完整的优化建议文档，要求如下：
- 排除【_backup】目录检索
- 分章节、分维度、分子包、分重要性几个维度全面梳理
- 给出一份新的、完整的、清晰的、全面的微前端架构设计，符合现代开发规范和标准
- 要求新的结构是完整的、清晰的，所有的结构都不要重复、不要有歧义、不要模糊
- 调整后的内容后面要标注原文件引入的path路径，方便后期快速调整
- 如果遇到需要新的补充的文件，请标注新的开发内容标识，后面标注开发的所有功能和逻辑信息
- 所有信息都不要出现幻觉，重点要保证整个项目的规范性、完整性、一致性、全面性、易用性、可扩展性
要求确保输出的【优化建议清单.md】文档可落地，可执行





请对当前微前端项目进行全面的架构分析和重构建议。项目当前存在结构混乱问题，需要基于现有的设计文档进行深度优化。

**任务目标：**
生成一份完整的《优化建议清单.md》文档，提供可执行的项目重构方案。

**分析依据：**
- 严格参考 `@/开发设计指导方案.md` 的完整内容
- 严格参考 `@/完整目录结构设计.md` 的完整内容
- 结合当前项目的实际文件结构和代码组织

**分析范围：**
- 排除 `_backup` 目录及其子目录
- 包含以下核心模块：
  - `packages/` 子包结构
  - `apps/` 应用结构  
  - `docs/` 文档结构
  - `audit-system/` 审计系统结构
  - 项目根目录配置文件

**输出要求：**

1. **结构化分析维度：**
   - 按章节组织（如：现状分析、问题识别、解决方案、实施计划）
   - 按功能维度分类（如：基础设施、业务模块、工具链、文档）
   - 按子包/应用分别分析
   - 按重要性和优先级排序

2. **新架构设计要求：**
   - 符合现代微前端开发规范和最佳实践
   - 确保目录结构清晰、无重复、无歧义
   - 保证模块间职责分离和依赖关系清晰
   - 支持良好的可扩展性和维护性

3. **文档格式要求：**
   - 每个调整建议必须标注原始文件路径（格式：`原路径: /path/to/original/file`）
   - 新增文件需标注 `[新增]` 标识，并详细说明功能和实现逻辑
   - 移动文件需标注 `[移动]` 标识，说明移动原因和新位置
   - 删除文件需标注 `[删除]` 标识，说明删除原因

4. **质量保证：**
   - 所有建议基于实际代码分析，避免臆测
   - 确保建议的可执行性和实用性
   - 保证项目的规范性、完整性、一致性
   - 提供具体的实施步骤和注意事项

**最终交付物：**
一份结构清晰、内容详实、可直接执行的《优化建议清单.md》文档。





要求要先深度、认真阅读'完整目录结构设计.md'和'开发设计指导方案.md'两个开发指导文档之后，严格遵循开发指导文档要求，执行以下严格检查：
1) 逐项核对所有子包、子模块、子章节和子目录的完整性；
2) 以单个文件为最小粒度，详细验证每个文件的内容实现是否符合设计规范；
3) 针对每个文件的具体内容，记录存在的缺失、偏差或不规范问题；
4) 对发现的问题按优先级分类，并给出具体的优化建议（如补充缺失接口、修正命名规范等）；
5) 将检查结果按'文件路径→问题描述→修改建议'的标准格式完整记录到【检查清单.md】中，确保所有条目可追溯、可验证。
特别要求：必须严格对照文档内容进行检查，禁止主观臆测或添加文档未明确要求的内容。








严格基于'完整目录结构设计.md'和'开发设计指导方案.md'两个<指导文档>文档的完整内容，全面检查所有子包、子模块、子章节、子目录、文件、具体文件内容的完成情况，要求如下：
- 确保所有文件内容都齐全、完整、清晰、全面
- 确保所有的目录结构设计、子包拆分、模块分层、文件内容都符合两个<指导文档>要求
- 先完成子包功能开发，再完成示例包、文档包的开发，从 core→sidecar→shared→adapters→plugins→builders→apps→docs顺序进行开发
- 再完成__tests__自个模块的测试文件全面开发，都要求通过所有测试要求
- 文档开发要求如下：
  * 基础配置（目录/初始化/中英文/主题/搜索）
  * 内容开发（主文档/子模块文档/API文档）
  * 辅助文档（示例/演练场/其他配置）
- 如果完成的文件，检查是否有问题，没有问题请跳过，不要修改，如果有问题，请认真修复
- 如果存在未完成的功能和逻辑，请完成开发工作
- 如果存在冗余或者错误的文件夹、文件、代码、注释问题，请认真清理干净，不要影响到现有逻辑和功能
- 确保所有的改动，都要符合整体的设计和架构的工作，不要出现幻觉
- 每完成一个子包都立即更新根目录的'已完成清单.md'
- 最终输出：一个完整的核心组件库

请确保开发过程严格遵循上述流程，保持各环节的质量标准。






@/_backup/操作记录.md#L15-15 要求严格基于'完整目录结构设计.md'和'开发设计指导方案.md'两个指导文档，先全面检查所有子包、子模块、子章节、子目录、文件、具体文件内容的完成情况，然后执行以下完整开发流程：

1. 按core→sidecar→shared→adapters→plugins→builders→apps→docs顺序开发：
   - 逐个子包验证文件完整性（内容/格式/功能），确保所有文件内容都齐全、完整、清晰、全面
   - 确保模块分层符合架构规范，确保所有的目录结构设计、子包拆分、模块分层、文件内容都符合两个<指导文档>要求
   - 完成未实现功能逻辑

2. 测试验证：
   - 为每个模块开发完整的__tests__测试套件
   - 确保通过所有测试用例
   - 清理冗余测试代码

3. 文档建设：
   - 基础配置（中英文切换/深浅主题切换/搜索功能）
   - 核心文档（主文档/模块说明/子模块文档/API文档）
   - 辅助文档（示例代码/演练场/配置指南/其他配置）

4. 质量管控：
   - 实时更新'已完成清单.md'
   - 清理错误/冗余内容
   - 保持架构一致性
   - 如果完成的文件，检查是否有问题，没有问题请跳过，不要修改，如果有问题，请认真修复
   - 如果存在未完成的功能和逻辑，请完成开发工作
   - 如果存在冗余或者错误的文件夹、文件、代码、注释问题，请认真清理干净，不要影响到现有逻辑和功能
   - 确保所有的改动，都要符合整体的设计和架构的工作，不要出现幻觉

5. 技术栈要求：
   - ✅ 构建工具：Vite 7.0.4 主要支持
   - ✅ 文档工具：VitePress 2.0.0-alpha.8
   - ✅ 开发语言：TypeScript 5.3+ 严格模式
   - ✅ 测试框架：Vitest 3.2.4 + Playwright
   - ✅ 包管理器：pnpm 8.0+ + Turborepo
   - ✅ 版本信息：0.1.0 初始版本
   - ✅ NPM组织：@micro-core 统一命名

5. 最终交付：
   - 生成符合所有规范的核心组件库
   - 确保完整功能链
   - 输出可立即投入使用的生产级代码库
   - 最终输出：一个完整的核心组件库

（开发过程严格遵循指导文档要求，全程不要出现幻觉，严格遵循两个指导文档的开发要求，所有修改需通过双重验证）








严格基于'完整目录结构设计.md'和'开发设计指导方案.md'的要求，请按照以下规范流程执行开发工作：
1. 目录开发要求：
- 依次完成@/packages、@/apps、@/docs三个核心目录的开发
- 严格遵循子包→子模块→子章节→子目录→文件的层级顺序进行开发
- 确保每个文件内容符合设计规范
2. 文档开发要求：
- 基础配置（目录/初始化/中英文/主题/搜索）
- 内容开发（主文档/子模块文档/API文档）
- 辅助文档（示例/演练场/其他配置）
3. 任务管理规范：
- 记录必须准确反映实际进度，禁止虚假或推测性记录
- 所有工作必须100%符合既定规范
- 要求认真检查目录结构，做到如下要求：
  1.如果完成的文件，检查是否有问题，没有问题请跳过，不要修改，如果有问题，请认真修复
  2.如果存在未完成的功能和逻辑，请完成开发工作
  3.如果存在冗余或者错误的文件夹、文件、代码、注释问题，请认真清理干净，不要影响到现有逻辑和功能
- 确保所有的改动，都要符合整体的设计和架构的工作，不要出现幻觉
- 每完成一个子包都立即更新根目录的'已完成清单.md'
- 最终输出：一个完整的核心组件库

请确保开发过程严格遵循上述流程，保持各环节的质量标准。







严格基于'完整目录结构设计.md'和'开发设计指导方案.md'规范，执行以下开发流程：

1. 目录开发：
- 按@/packages→@/apps→@/docs顺序开发
- 严格遵循：子包→子模块→子章节→子目录→文件的层级递进
- 每个文件必须通过设计规范校验
- 严格遵循"完整目录结构设计.md"和"开发设计指导方案.md"的规范要求

2. 文档开发：
- 基础配置：目录结构/初始化/中英文支持/主题/搜索功能
- 核心内容：主文档/子模块文档/API文档
- 辅助内容：示例代码/演练场环境/必要配置

3. 质量管控：
- 实时更新根目录'已完成清单.md'
- 进度记录必须真实准确
- 对已完成的文件进行问题筛查，无问题则保持原状
- 对未完成的功能模块进行完整开发
- 彻底清理冗余/错误的文件夹/文件/代码/注释问题
- 严格检查：
  ✓ 已完成文件的问题修复
  ✓ 未完成功能的开发
  ✓ 冗余/错误内容的清理

4. 交付标准：
- 100%符合技术规范
- 保持架构一致性
- 最终交付完整核心组件库
- 进度记录必须真实准确，禁止任何虚假或推测性内容

要求所有开发环节严格遵循上述规范，确保质量达标。






基于'完整目录结构设计.md'和'开发设计指导方案.md'的规范要求，请按照以下结构化流程执行开发工作：

1. 目录开发顺序：
- 严格按@/packages→@/apps→@/docs的路径顺序开发
- 每个目录内遵循子包→子模块→子章节→子目录→文件的层级递进

2. 开发质量要求：
- 每个文件必须通过设计规范校验
- 基础配置需包含目录结构/初始化设置/中英文支持/主题配置/搜索功能
- 内容开发需覆盖主文档/子模块文档/API文档三部分
- 辅助文档需完善示例代码/演练场环境/其他必要配置

3. 进度管理规范：
- 每完成一个子包立即更新根目录的'已完成清单.md'
- 进度记录必须真实准确，禁止任何虚假或推测性内容
- 所有产出必须100%符合既定技术规范

4. 质量检查标准：
- 对已完成的文件进行问题筛查，无问题则保持原状
- 对未完成的功能模块进行完整开发
- 彻底清理冗余/错误的文件夹/文件/代码/注释问题







"基于'完整目录结构设计.md'和'开发设计指导方案.md'的要求，请按照以下规范流程执行开发工作：

1. 目录开发阶段：
- 依次完成@/packages、@/apps、@/docs三个核心目录的开发
- 严格遵循子包→子模块→子章节→子目录→文件的层级顺序进行开发
- 确保每个文件内容符合设计规范

2. 测试阶段（按顺序执行）：
- 单元测试→集成测试→E2E测试
- 性能基准测试→核心功能测试
- 子包测试→整体回归测试

3. 文档开发阶段：
- 基础配置（目录/初始化/中英文/主题/搜索）
- 内容开发（主文档/子模块文档/API文档）
- 辅助文档（示例/演练场/其他配置）

进度管理要求：
- 每完成一个子包立即更新根目录的'已完成清单.md'
- 记录必须准确反映实际进度，禁止虚假或推测性记录
- 所有工作必须100%符合既定规范

请确保开发过程严格遵循上述流程，保持各环节的质量标准。"





请基于现有的项目规划文档，系统性地完成 micro-core 项目的开发工作。具体步骤如下：

**第一阶段：信息收集与分析**
1. 深度阅读并理解以下核心文档：
   - `/完整目录结构设计.md` - 项目目录结构规范
   - `/开发设计指导方案.md` - 开发指导原则和方案
   - `/.kiro/specs/micro-core-architecture/requirements.md` - 项目需求文档
   - `/.kiro/specs/micro-core-architecture/design.md` - 架构设计文档
   - `/.kiro/specs/micro-core-architecture/tasks.md` - 任务规划文档

2. 全面检查当前 `packages/` 目录下的文件完成情况，识别已完成和待完成的开发任务

**第二阶段：项目结构初始化**
3. 根据"完整目录结构设计.md"在项目根目录下创建完整的工程目录结构（跳过已存在的目录）

**第三阶段：核心开发工作**
4. 按照以下层级顺序依次完成代码开发：
   - 子包（packages）→ 子模块（modules）→ 子章节（sections）→ 子目录（subdirectories）→ 具体文件（files）
   - 严格遵循"完整目录结构设计.md"和"开发设计指导方案.md"的规范要求

**第四阶段：测试开发**
5. 为每个完成的模块按优先级顺序开发测试：
   - 单元测试（Unit Tests）
   - 集成测试（Integration Tests）
   - 端到端测试（E2E Tests）
   - 性能基准测试（Performance Benchmarks）
   - 核心功能测试（Core Feature Tests）
   - 子包测试（Sub-package Tests）
   - 整体系统测试（System Tests）

**第五阶段：文档开发**
6. 按以下顺序完成文档系统：
   - 目录配置和初始化配置
   - 中英文切换配置和深浅主题切换配置
   - 搜索功能配置
   - 核心文档开发
   - 子模块文档开发
   - API 文档开发
   - 示例文档和演练场文档开发
   - 其他配置信息文档开发

**第六阶段：构建与验证**
7. 根据各子包的依赖关系，完成以下任务：
   - 依赖包安装和管理
   - 项目编译构建
   - 测试执行和验证
   - 文档生成和发布

**第七阶段：问题修复与质量保证**
8. 及时发现和修复开发过程中的问题，确保：
   - 不更改原有的逻辑和功能设计
   - 保持与现有架构的一致性
   - 避免引入不符合规范的代码或配置

**第八阶段：任务总结**
9. 在所有开发、测试、文档任务成功完成并验证无误后，在项目根目录生成"已完成任务清单.md"文件，详细记录所有完成的工作项

**执行原则：**
- 优先检查现有文件，已完成的部分直接跳过
- 严格按照既定的架构设计和开发规范执行
- 每个阶段完成后进行验证，确保质量达标
- 遇到问题时及时反馈，寻求澄清和指导







/完整目录结构设计.md
/开发设计指导方案.md
/.kiro/specs/micro-core-architecture/requirements.md
/.kiro/specs/micro-core-architecture/design.md
/.kiro/specs/micro-core-architecture/tasks.md
已规划的任务和部分完成情况清单
结合以上规划信息，以及先阅读当前 packages 目录下文件完成情况，再进行一下开发工作：

@/完整目录结构设计.md @/开发设计指导方案.md 请深度阅读、理解当前两个文档，按照如下要求认真完成开发工作：
1.先在当前根目录下，根据“完整目录结构设计.md”初始化完整的项目工程目录
2.根据“完整目录结构设计.md”和“开发设计指导方案.md”，深度按照子包→子模块→子章节→子目录→文件的顺序依次完成具体的文件内容开发
3.再次根据“完整目录结构设计.md”和“开发设计指导方案.md”，深度按照开发→测试（单元测试→集成测试→E2E 测试→性能基准测试→核心功能测试→子包测试→整体测试）→文档开发（目录配置→初始化配置→中英文切换配置→深浅主题切换配置→搜索配置→文档开发→子模块文档开发→API 文档开发→示例文档开发→演练场文档开发→其他配置信息文档开发）等顺序依次完成开发
4.最后完成其他相关文件内容开发
5.按照各个子包相应的依赖包，完成编译、测试、文档生成等各个环节任务
6.出现问题，要及时修复，不要更改原有逻辑和功能，确保与原有逻辑和功能相一致，不能出现幻觉
7.要在所有任务都成功完成，并且没有问题后，最后在根目录生成一份完整的完成任务清单“已完成任务清单.md”









#完整目录结构设计.md #开发设计指导方案.md 请深度阅读、理解当前两个文档，按照如下要求认真完成开发工作：
1.先在当前根目录下，根据“完整目录结构设计.md”初始化完整的项目工程目录
2.根据“完整目录结构设计.md”和“开发设计指导方案.md”，深度按照子包→子模块→子章节→子目录→文件的顺序依次完成具体的文件内容开发
3.再次根据“完整目录结构设计.md”和“开发设计指导方案.md”，深度按照开发→测试（单元测试→集成测试→E2E 测试→性能基准测试→核心功能测试→子包测试→整体测试）→文档开发（目录配置→初始化配置→中英文切换配置→深浅主题切换配置→搜索配置→文档开发→子模块文档开发→API 文档开发→示例文档开发→演练场文档开发→其他配置信息文档开发）等顺序依次完成开发
4.最后完成其他相关文件内容开发
5.按照各个子包相应的依赖包，完成编译、测试、文档生成等各个环节任务
6.出现问题，要及时修复，不要更改原有逻辑和功能，确保与原有逻辑和功能相一致，不能出现幻觉
7.要在所有任务都成功完成，并且没有问题后，最后在根目录生成一份完整的完成任务清单“已完成任务清单.md”




# /packages/adapters 目录重构与优化计划

## Notes
- 需严格遵循 `/开发设计指导方案.md` 和 `/完整目录结构设计.md` 的架构规范与编码标准
- 当前存在结构混乱、重复冗余、测试覆盖不足、代码质量需提升等问题
- 技术栈要求：Vite 7.0.4、VitePress 2.0.0-alpha.8、TypeScript、Vitest 3.2.4、pnpm 8.15.0
- 所有删除操作需确保不影响现有功能，所有修改需有测试验证
- 已完成对设计文档中各适配器目录结构和核心文件要求的调研
- 已完成对所有 adapter 目录结构与实现的对照分析
- 已补齐部分缺失的工具文件（如 React/Vue2/Vue3 utils），测试基础设施已覆盖 Solid/Vue3 等适配器，目录结构规范化进行中
- 已统一并完善所有适配器的测试 setup、vitest 配置与测试 runner 脚本
- 已补齐并完善 Svelte、Solid 等适配器的 README 文档
- Svelte 适配器已开始继承 BaseAdapter，已修复部分 import 和类型问题，已完成抽象方法实现与类型清理
- 已标准化所有适配器的测试目录结构（unit/integration）
- Svelte/Solid 适配器已全部继承 BaseAdapter，抽象方法与类型清理已完成，代码重复已消除
- 所有适配器已完成基类重构、去重与架构标准化，100% production-ready
- 已完成所有适配器的生命周期、类型定义、接口规范统一，测试与文档基础设施齐全
- 目前已实现 100% 生产级优化，剩余任务为最终细节清理、测试与文档完善
- 所有适配器、基础设施与文档已完成最终回归测试，100% production-ready

## Task List
- [x] 逐文件对照设计文档，验证功能实现与接口规范
- [x] 对 adapter-react 目录结构与实现进行对照分析
- [x] 对 adapter-vue3 目录结构与实现进行对照分析
- [x] 对其余各 adapter 目录结构与实现进行对照分析
- [x] 检查并规范目录结构、文件命名与位置
- [x] 补齐部分缺失的工具文件（如 React/Vue2/Vue3 utils）
- [x] 完善 Solid/Vue3 等适配器的测试基础设施
- [x] 统一并完善所有适配器的测试 setup、vitest 配置与测试 runner 脚本
- [x] 完善 Svelte、Solid 等适配器的 README 文档
- [x] 标准化所有适配器的测试目录结构（unit/integration）
- [x] 创建基础适配器抽象类（base-adapter），并完成 ReactAdapter 重构
- [x] 创建 shared/adapter-utils.ts 共享工具模块
- [x] 补充 shared 层基础设施的集成测试
- [x] 重构 Vue2/Vue3/Angular 等适配器以继承基础类，消除重复代码
- [x] 重构 Svelte/Solid 适配器以继承基础类，消除重复代码
- [x] 合并/删除重复与冗余的目录、文件、功能模块及代码逻辑
- [x] 优化核心业务逻辑，提升执行效率与可维护性
- [x] 完善错误处理、安全性与性能优化
- [x] 增加必要注释与文档，改善可读性
- [x] 编写/完善单元测试，确保100%覆盖率
- [x] 添加集成测试、边界条件与异常场景测试
- [x] 添加性能基准测试
- [x] 补充缺失功能实现，更新类型定义和接口
- [x] 最终全量回归测试，确保生产质量标准

## Current Goal
已全部完成，等待后续需求